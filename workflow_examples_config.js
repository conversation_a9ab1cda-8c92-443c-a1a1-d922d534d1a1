/*
 * 金蝶云苍穹工作流配置示例
 * 包含各种业务场景的验证规则和变量映射配置
 * 基于金蝶云社区最佳实践编写
 */

/**
 * 费用报销单验证配置
 */
var expenseValidationConfig = {
    businessType: "expense",
    maxAmount: 50000,
    minEntryCount: 2,
    maxEntryCount: 50,
    allowedAccounts: ["6601", "6602", "6603", "6604"], // 费用类科目
    forbiddenAccounts: [],
    specialRules: {
        cashLimit: 10000, // 现金限额
        requiresReceipt: true, // 需要发票
        categoryRequired: true // 需要费用类别
    }
};

/**
 * 付款单验证配置
 */
var paymentValidationConfig = {
    businessType: "payment",
    maxAmount: 1000000,
    minEntryCount: 2,
    maxEntryCount: 20,
    allowedAccounts: ["1002", "1012", "2202"], // 银行存款、其他货币资金、应付账款
    forbiddenAccounts: ["1001"], // 禁止现金（大额付款）
    specialRules: {
        contractRequired: 100000, // 超过此金额需要合同
        approvalRequired: 50000, // 超过此金额需要特殊审批
        bankOnly: true // 只允许银行转账
    }
};

/**
 * 收款单验证配置
 */
var receiptValidationConfig = {
    businessType: "receipt",
    maxAmount: 5000000,
    minEntryCount: 2,
    maxEntryCount: 10,
    allowedAccounts: ["1001", "1002", "1012"], // 货币资金科目
    forbiddenAccounts: [],
    specialRules: {
        creditCheckLimit: 50000, // 超过此金额需要信用检查
        invoiceRequired: true, // 需要发票
        customerRequired: true // 需要客户信息
    }
};

/**
 * 采购入库单验证配置
 */
var purchaseValidationConfig = {
    businessType: "purchase",
    maxAmount: 2000000,
    minEntryCount: 2,
    maxEntryCount: 100,
    allowedAccounts: ["1401", "1402", "2202"], // 原材料、库存商品、应付账款
    forbiddenAccounts: [],
    specialRules: {
        supplierRequired: true, // 需要供应商信息
        contractRequired: 100000, // 超过此金额需要合同
        qualityCheckRequired: true // 需要质量检查
    }
};

/**
 * 销售出库单验证配置
 */
var salesValidationConfig = {
    businessType: "sales",
    maxAmount: ********,
    minEntryCount: 2,
    maxEntryCount: 50,
    allowedAccounts: ["1122", "6001", "1401"], // 应收账款、主营业务收入、原材料
    forbiddenAccounts: [],
    specialRules: {
        customerRequired: true, // 需要客户信息
        deliveryRequired: true, // 需要发货信息
        discountLimit: 0.1 // 折扣限制10%
    }
};

/**
 * 审批级别配置
 */
var approvalLevelConfig = {
    L1: {
        maxAmount: 10000,
        description: "一级审批：部门经理",
        requiredRoles: ["DEPT_MANAGER"],
        timeLimit: 24 // 小时
    },
    L2: {
        maxAmount: 100000,
        description: "二级审批：财务经理",
        requiredRoles: ["DEPT_MANAGER", "FINANCE_MANAGER"],
        timeLimit: 48 // 小时
    },
    L3: {
        maxAmount: 1000000,
        description: "三级审批：总经理",
        requiredRoles: ["DEPT_MANAGER", "FINANCE_MANAGER", "GENERAL_MANAGER"],
        timeLimit: 72 // 小时
    },
    L4: {
        maxAmount: Infinity,
        description: "四级审批：董事长",
        requiredRoles: ["DEPT_MANAGER", "FINANCE_MANAGER", "GENERAL_MANAGER", "CHAIRMAN"],
        timeLimit: 168 // 小时（一周）
    }
};

/**
 * 条件分支配置
 */
var conditionalBranchConfig = [
    {
        name: "高额交易检查",
        condition: "${TotalAmount} > 500000",
        variables: {
            "RequiresCEOApproval": true,
            "RiskLevel": "HIGH",
            "RequiresAudit": true
        }
    },
    {
        name: "现金交易检查", 
        condition: "${HasCashEntries} === true && ${TotalAmount} > 10000",
        variables: {
            "RequiresCashApproval": true,
            "CashControlRequired": true,
            "RequiresJustification": true
        }
    },
    {
        name: "错误处理",
        condition: "${ErrorCount} > 0",
        variables: {
            "NeedsCorrection": true,
            "ApprovalPath": "reject",
            "RequiresResubmission": true
        }
    },
    {
        name: "警告处理",
        condition: "${WarningCount} > 0 && ${TotalAmount} < 10000",
        variables: {
            "AllowProceedWithWarnings": true,
            "RequiresAttention": true,
            "NeedsReview": true
        }
    },
    {
        name: "外币交易检查",
        condition: "${HasForeignCurrency} === true",
        variables: {
            "RequiresExchangeRateApproval": true,
            "CurrencyRiskLevel": "MEDIUM",
            "RequiresHedging": true
        }
    }
];

/**
 * 业务类型与配置的映射
 */
var businessTypeConfigMapping = {
    "expense": expenseValidationConfig,
    "payment": paymentValidationConfig,
    "receipt": receiptValidationConfig,
    "purchase": purchaseValidationConfig,
    "sales": salesValidationConfig
};

/**
 * 标准流程变量映射
 */
var standardVariableMapping = {
    // 验证结果变量
    validationStatus: "IsFormValid",
    errorCount: "ErrorCount",
    warningCount: "WarningCount",
    errorMessages: "ValidationErrors",
    warningMessages: "ValidationWarnings",
    
    // 分录统计变量
    entryCount: "EntryCount",
    totalDebitAmount: "TotalDebitAmount",
    totalCreditAmount: "TotalCreditAmount",
    
    // 审批流程变量
    approvalPath: "ApprovalPath",
    approvalLevel: "ApprovalLevel",
    requiresAttention: "RequiresAttention",
    rejectReason: "RejectReason",
    
    // 业务相关变量
    businessType: "BusinessType",
    totalAmount: "TotalAmount",
    riskLevel: "RiskLevel"
};

/**
 * 扩展变量映射（包含更多业务字段）
 */
var extendedVariableMapping = {
    // 继承标准映射
    ...standardVariableMapping,
    
    // 扩展字段
    maxSingleAmount: "MaxSingleEntryAmount",
    hasHighValueEntries: "HasHighValueEntries",
    requiresSpecialApproval: "RequiresSpecialApproval",
    hasCashEntries: "HasCashEntries",
    hasForeignCurrency: "HasForeignCurrency",
    requiresContract: "RequiresContract",
    requiresCreditCheck: "RequiresCreditCheck",
    requiresAudit: "RequiresAudit"
};

/**
 * 根据业务类型获取验证配置
 * @param {String} businessType - 业务类型
 * @returns {Object} 验证配置对象
 */
function getValidationConfigByBusinessType(businessType) {
    return businessTypeConfigMapping[businessType] || {
        businessType: "general",
        maxAmount: *********,
        minEntryCount: 2,
        maxEntryCount: 100,
        allowedAccounts: [],
        forbiddenAccounts: [],
        specialRules: {}
    };
}

/**
 * 根据金额确定审批级别
 * @param {Number} amount - 金额
 * @returns {String} 审批级别
 */
function determineApprovalLevelByAmount(amount) {
    for (var level in approvalLevelConfig) {
        if (amount <= approvalLevelConfig[level].maxAmount) {
            return level;
        }
    }
    return "L4"; // 默认最高级别
}

/**
 * 获取审批级别详细信息
 * @param {String} level - 审批级别
 * @returns {Object} 审批级别详细信息
 */
function getApprovalLevelDetails(level) {
    return approvalLevelConfig[level] || approvalLevelConfig["L1"];
}

/**
 * 创建业务特定的变量映射
 * @param {String} businessType - 业务类型
 * @returns {Object} 变量映射配置
 */
function createBusinessSpecificVariableMapping(businessType) {
    var mapping = {...standardVariableMapping};
    
    switch (businessType) {
        case "expense":
            mapping.expenseCategory = "ExpenseCategory";
            mapping.requiresReceipt = "RequiresReceipt";
            mapping.departmentApproval = "DepartmentApproval";
            break;
        case "payment":
            mapping.paymentMethod = "PaymentMethod";
            mapping.supplierApproval = "SupplierApproval";
            mapping.contractRequired = "ContractRequired";
            break;
        case "receipt":
            mapping.receiptType = "ReceiptType";
            mapping.customerApproval = "CustomerApproval";
            mapping.invoiceRequired = "InvoiceRequired";
            break;
        case "purchase":
            mapping.supplierRequired = "SupplierRequired";
            mapping.qualityCheckRequired = "QualityCheckRequired";
            mapping.budgetCheck = "BudgetCheck";
            break;
        case "sales":
            mapping.customerRequired = "CustomerRequired";
            mapping.deliveryRequired = "DeliveryRequired";
            mapping.discountApproval = "DiscountApproval";
            break;
    }
    
    return mapping;
}

/**
 * 验证配置的有效性
 * @param {Object} config - 配置对象
 * @returns {Boolean} 配置是否有效
 */
function validateConfig(config) {
    if (!config || typeof config !== "object") {
        return false;
    }
    
    // 检查必需的配置项
    var requiredFields = ["businessType", "maxAmount", "minEntryCount", "maxEntryCount"];
    for (var i = 0; i < requiredFields.length; i++) {
        if (!(requiredFields[i] in config)) {
            return false;
        }
    }
    
    // 检查数值的合理性
    if (config.maxAmount <= 0 || config.minEntryCount < 1 || config.maxEntryCount < config.minEntryCount) {
        return false;
    }
    
    return true;
}

/**
 * 合并配置对象
 * @param {Object} baseConfig - 基础配置
 * @param {Object} customConfig - 自定义配置
 * @returns {Object} 合并后的配置
 */
function mergeConfigs(baseConfig, customConfig) {
    var mergedConfig = {...baseConfig};
    
    for (var key in customConfig) {
        if (customConfig.hasOwnProperty(key)) {
            if (typeof customConfig[key] === "object" && !Array.isArray(customConfig[key])) {
                mergedConfig[key] = {...mergedConfig[key], ...customConfig[key]};
            } else {
                mergedConfig[key] = customConfig[key];
            }
        }
    }
    
    return mergedConfig;
}

// 导出配置对象和函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        expenseValidationConfig: expenseValidationConfig,
        paymentValidationConfig: paymentValidationConfig,
        receiptValidationConfig: receiptValidationConfig,
        purchaseValidationConfig: purchaseValidationConfig,
        salesValidationConfig: salesValidationConfig,
        approvalLevelConfig: approvalLevelConfig,
        conditionalBranchConfig: conditionalBranchConfig,
        businessTypeConfigMapping: businessTypeConfigMapping,
        standardVariableMapping: standardVariableMapping,
        extendedVariableMapping: extendedVariableMapping,
        getValidationConfigByBusinessType: getValidationConfigByBusinessType,
        determineApprovalLevelByAmount: determineApprovalLevelByAmount,
        getApprovalLevelDetails: getApprovalLevelDetails,
        createBusinessSpecificVariableMapping: createBusinessSpecificVariableMapping,
        validateConfig: validateConfig,
        mergeConfigs: mergeConfigs
    };
}
