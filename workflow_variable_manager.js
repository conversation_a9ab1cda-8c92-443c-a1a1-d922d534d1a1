/*
 * 金蝶云苍穹工作流流程变量管理脚本
 * 适用于金蝶云苍穹流程服务云工作流节点
 * 基于金蝶云社区最佳实践编写
 */

/**
 * 设置流程变量
 * @param {String} variableName - 变量名称
 * @param {*} variableValue - 变量值
 * @param {String} dataType - 数据类型 (可选)
 * @returns {Boolean} 设置是否成功
 */
function setProcessVariable(variableName, variableValue, dataType) {
    try {
        if (!variableName || variableName.trim() === "") {
            throw new Error("变量名称不能为空");
        }
        
        // 根据数据类型转换变量值
        var convertedValue = convertValueByType(variableValue, dataType);
        
        // 使用金蝶云苍穹工作流API设置变量
        this.setVariable(variableName, convertedValue);
        
        return true;
    } catch (error) {
        // 记录错误到系统变量
        this.setVariable("LastVariableError", "设置变量" + variableName + "失败: " + error.message);
        return false;
    }
}

/**
 * 获取流程变量
 * @param {String} variableName - 变量名称
 * @returns {*} 变量值
 */
function getProcessVariable(variableName) {
    try {
        if (!variableName || variableName.trim() === "") {
            throw new Error("变量名称不能为空");
        }
        
        return this.getVariable(variableName);
    } catch (error) {
        this.setVariable("LastVariableError", "获取变量" + variableName + "失败: " + error.message);
        return null;
    }
}

/**
 * 批量设置流程变量
 * @param {Object} variableMap - 变量映射对象 {变量名: 变量值}
 * @returns {Object} 设置结果
 */
function setMultipleVariables(variableMap) {
    var result = {
        success: true,
        successCount: 0,
        failureCount: 0,
        errors: []
    };
    
    try {
        for (var varName in variableMap) {
            if (variableMap.hasOwnProperty(varName)) {
                var success = setProcessVariable(varName, variableMap[varName]);
                if (success) {
                    result.successCount++;
                } else {
                    result.failureCount++;
                    result.errors.push("设置变量 " + varName + " 失败");
                }
            }
        }
        
        if (result.failureCount > 0) {
            result.success = false;
        }
        
    } catch (error) {
        result.success = false;
        result.errors.push("批量设置变量时发生错误: " + error.message);
    }
    
    return result;
}

/**
 * 根据表单验证结果设置相关流程变量
 * @param {Object} validationResult - 验证结果对象
 * @returns {Boolean} 设置是否成功
 */
function setVariablesFromValidation(validationResult) {
    try {
        var variables = {};
        
        // 基本验证结果变量
        variables["IsFormValid"] = validationResult.isValid;
        variables["ErrorCount"] = validationResult.errors ? validationResult.errors.length : 0;
        variables["WarningCount"] = validationResult.warnings ? validationResult.warnings.length : 0;
        
        // 错误和警告信息
        if (validationResult.errors && validationResult.errors.length > 0) {
            variables["ValidationErrors"] = validationResult.errors.join("; ");
        }
        
        if (validationResult.warnings && validationResult.warnings.length > 0) {
            variables["ValidationWarnings"] = validationResult.warnings.join("; ");
        }
        
        // 分录统计信息
        if (validationResult.entryCount !== undefined) {
            variables["EntryCount"] = validationResult.entryCount;
        }
        
        if (validationResult.totalDebit !== undefined) {
            variables["TotalDebitAmount"] = validationResult.totalDebit;
        }
        
        if (validationResult.totalCredit !== undefined) {
            variables["TotalCreditAmount"] = validationResult.totalCredit;
        }
        
        // 批量设置变量
        var result = setMultipleVariables(variables);
        return result.success;
        
    } catch (error) {
        this.setVariable("LastVariableError", "根据验证结果设置变量失败: " + error.message);
        return false;
    }
}

/**
 * 根据条件设置流程变量
 * @param {Array} conditions - 条件数组
 * @returns {Boolean} 设置是否成功
 */
function setVariablesByConditions(conditions) {
    try {
        for (var i = 0; i < conditions.length; i++) {
            var condition = conditions[i];
            
            // 评估条件
            if (evaluateCondition(condition.condition)) {
                // 条件满足，设置对应的变量
                var success = setProcessVariable(
                    condition.variableName,
                    condition.variableValue,
                    condition.dataType
                );
                
                if (!success) {
                    return false;
                }
            }
        }
        
        return true;
    } catch (error) {
        this.setVariable("LastVariableError", "根据条件设置变量失败: " + error.message);
        return false;
    }
}

/**
 * 评估条件表达式
 * @param {String} conditionExpr - 条件表达式
 * @returns {Boolean} 条件是否满足
 */
function evaluateCondition(conditionExpr) {
    try {
        // 简单的条件评估实现
        // 支持基本的比较操作，如：${变量名} > 100
        
        // 替换变量占位符
        var expr = conditionExpr;
        var variablePattern = /\$\{([^}]+)\}/g;
        var match;
        
        while ((match = variablePattern.exec(conditionExpr)) !== null) {
            var variableName = match[1];
            var variableValue = getProcessVariable(variableName);
            
            // 根据值类型进行替换
            if (typeof variableValue === "string") {
                expr = expr.replace(match[0], "'" + variableValue + "'");
            } else {
                expr = expr.replace(match[0], variableValue);
            }
        }
        
        // 使用eval评估表达式（注意：生产环境建议使用更安全的表达式解析器）
        return eval(expr);
    } catch (error) {
        this.setVariable("LastVariableError", "评估条件表达式失败: " + error.message);
        return false;
    }
}

/**
 * 根据数据类型转换变量值
 * @param {*} value - 原始值
 * @param {String} dataType - 目标数据类型
 * @returns {*} 转换后的值
 */
function convertValueByType(value, dataType) {
    if (value === null || value === undefined) {
        return value;
    }
    
    if (!dataType) {
        return value;
    }
    
    switch (dataType.toLowerCase()) {
        case "string":
            return String(value);
        case "number":
        case "decimal":
        case "int":
        case "integer":
            var num = parseFloat(value);
            if (isNaN(num)) {
                throw new Error("无法将值转换为数字: " + value);
            }
            return num;
        case "boolean":
        case "bool":
            if (typeof value === "boolean") {
                return value;
            }
            if (typeof value === "string") {
                return value.toLowerCase() === "true" || value === "1";
            }
            if (typeof value === "number") {
                return value !== 0;
            }
            return Boolean(value);
        case "date":
        case "datetime":
            if (value instanceof Date) {
                return value;
            }
            if (typeof value === "string") {
                return new Date(value);
            }
            throw new Error("无法将值转换为日期: " + value);
        default:
            return value;
    }
}

/**
 * 根据金额设置审批级别
 * @param {Number} amount - 金额
 * @returns {String} 审批级别
 */
function setApprovalLevelByAmount(amount) {
    try {
        var approvalLevel = "L1"; // 默认一级审批
        
        if (amount > 1000000) {
            approvalLevel = "L4"; // 四级审批：董事长
        } else if (amount > 100000) {
            approvalLevel = "L3"; // 三级审批：总经理
        } else if (amount > 10000) {
            approvalLevel = "L2"; // 二级审批：财务经理
        }
        
        // 设置审批级别变量
        setProcessVariable("ApprovalLevel", approvalLevel);
        setProcessVariable("ApprovalAmount", amount);
        
        // 设置审批路径
        if (amount > 0) {
            setProcessVariable("ApprovalPath", "normal");
        } else {
            setProcessVariable("ApprovalPath", "reject");
            setProcessVariable("RejectReason", "金额不能为零或负数");
        }
        
        return approvalLevel;
        
    } catch (error) {
        this.setVariable("LastVariableError", "设置审批级别失败: " + error.message);
        return "L1";
    }
}

/**
 * 设置业务相关的流程变量
 * @param {String} businessType - 业务类型
 * @param {Object} businessData - 业务数据
 * @returns {Boolean} 设置是否成功
 */
function setBusinessVariables(businessType, businessData) {
    try {
        var variables = {};
        
        // 设置业务类型
        variables["BusinessType"] = businessType;
        
        // 根据业务类型设置特定变量
        switch (businessType) {
            case "expense":
                variables["RequiresCashApproval"] = businessData.hasCashEntries || false;
                variables["ExpenseCategory"] = businessData.category || "general";
                break;
            case "payment":
                variables["PaymentMethod"] = businessData.paymentMethod || "bank";
                variables["RequiresContract"] = businessData.amount > 100000;
                break;
            case "receipt":
                variables["ReceiptType"] = businessData.receiptType || "sales";
                variables["RequiresCreditCheck"] = businessData.amount > 50000;
                break;
        }
        
        // 批量设置变量
        var result = setMultipleVariables(variables);
        return result.success;
        
    } catch (error) {
        this.setVariable("LastVariableError", "设置业务变量失败: " + error.message);
        return false;
    }
}

/**
 * 工作流变量管理主入口函数
 * @param {Object} config - 配置对象
 * @returns {Boolean} 执行是否成功
 */
function manageWorkflowVariables(config) {
    try {
        // 记录开始时间
        setProcessVariable("VariableManageStartTime", new Date().toISOString());
        
        var success = true;
        
        // 根据配置执行不同的变量管理操作
        if (config.validationResult) {
            success = success && setVariablesFromValidation(config.validationResult);
        }
        
        if (config.approvalAmount) {
            setApprovalLevelByAmount(config.approvalAmount);
        }
        
        if (config.businessType && config.businessData) {
            success = success && setBusinessVariables(config.businessType, config.businessData);
        }
        
        if (config.conditions) {
            success = success && setVariablesByConditions(config.conditions);
        }
        
        if (config.customVariables) {
            var result = setMultipleVariables(config.customVariables);
            success = success && result.success;
        }
        
        // 记录结束时间和结果
        setProcessVariable("VariableManageEndTime", new Date().toISOString());
        setProcessVariable("VariableManageResult", success ? "success" : "failed");
        
        return success;
        
    } catch (error) {
        setProcessVariable("VariableManageResult", "error");
        setProcessVariable("VariableManageError", error.message);
        return false;
    }
}
