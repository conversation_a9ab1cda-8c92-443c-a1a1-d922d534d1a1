/**
 * 分录获取配置文件
 * 包含数据库连接配置、字段映射等
 */

// 数据库配置
var dbConfig = {
    server: "localhost",
    database: "kingdee_db",
    username: "your_username",
    password: "your_password",
    port: 1433
};

// 分录表字段映射
var entryFieldMapping = {
    // 主表字段
    voucher: {
        tableName: "t_voucher",
        fields: {
            voucherNumber: "voucher_number",
            voucherDate: "voucher_date",
            period: "period",
            voucherType: "voucher_type",
            status: "status"
        }
    },
    
    // 分录表字段
    entry: {
        tableName: "t_voucher_entry",
        fields: {
            entryId: "entry_id",
            voucherId: "voucher_id",
            entrySeq: "entry_seq",
            accountId: "account_id",
            accountCode: "account_code",
            accountName: "account_name",
            debitAmount: "debit_amount",
            creditAmount: "credit_amount",
            summary: "summary",
            currencyId: "currency_id",
            exchangeRate: "exchange_rate",
            originalDebit: "original_debit",
            originalCredit: "original_credit"
        }
    },
    
    // 辅助项表字段
    auxItem: {
        tableName: "t_voucher_entry_aux",
        fields: {
            entryId: "entry_id",
            auxType: "aux_type",
            auxId: "aux_id",
            auxCode: "aux_code",
            auxName: "aux_name"
        }
    }
};

// 查询模板
var queryTemplates = {
    // 根据凭证号获取分录
    byVoucherNumber: `
        SELECT 
            e.entry_id,
            e.entry_seq,
            e.account_code,
            e.account_name,
            e.debit_amount,
            e.credit_amount,
            e.summary,
            e.currency_id,
            e.exchange_rate,
            v.voucher_number,
            v.voucher_date,
            v.period
        FROM t_voucher_entry e
        INNER JOIN t_voucher v ON e.voucher_id = v.voucher_id
        WHERE v.voucher_number = ?
        ORDER BY e.entry_seq
    `,
    
    // 根据期间获取分录
    byPeriod: `
        SELECT 
            e.entry_id,
            e.entry_seq,
            e.account_code,
            e.account_name,
            e.debit_amount,
            e.credit_amount,
            e.summary,
            v.voucher_number,
            v.voucher_date,
            v.period
        FROM t_voucher_entry e
        INNER JOIN t_voucher v ON e.voucher_id = v.voucher_id
        WHERE v.period = ?
        ORDER BY v.voucher_number, e.entry_seq
    `,
    
    // 根据科目代码获取分录
    byAccountCode: `
        SELECT 
            e.entry_id,
            e.entry_seq,
            e.account_code,
            e.account_name,
            e.debit_amount,
            e.credit_amount,
            e.summary,
            v.voucher_number,
            v.voucher_date,
            v.period
        FROM t_voucher_entry e
        INNER JOIN t_voucher v ON e.voucher_id = v.voucher_id
        WHERE e.account_code LIKE ?
        ORDER BY v.voucher_date, v.voucher_number, e.entry_seq
    `,
    
    // 获取辅助项信息
    auxItems: `
        SELECT 
            aux_type,
            aux_id,
            aux_code,
            aux_name
        FROM t_voucher_entry_aux
        WHERE entry_id = ?
        ORDER BY aux_type
    `
};

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        dbConfig: dbConfig,
        entryFieldMapping: entryFieldMapping,
        queryTemplates: queryTemplates
    };
}
