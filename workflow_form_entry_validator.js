/*
 * 金蝶云苍穹工作流表单分录验证脚本
 * 适用于金蝶云苍穹流程服务云工作流节点
 * 基于金蝶云社区最佳实践编写
 */

/**
 * 主验证函数 - 验证表单分录数据
 * @param {Object} context - 工作流上下文对象
 * @returns {Boolean} 验证是否通过
 */
function validateFormEntries(context) {
    try {
        // 获取表单数据
        var formData = this.getFormData();
        if (!formData) {
            this.setVariable("ValidationResult", "表单数据为空");
            return false;
        }
        
        // 获取分录数据 - 根据实际表单调整分录实体名称
        var entryData = formData["FEntity"]; // 通用分录实体名称
        if (!entryData || entryData.length === 0) {
            this.setVariable("ValidationResult", "分录数据为空");
            this.setVariable("EntryCount", 0);
            return false;
        }
        
        // 执行分录验证
        var validationResult = performEntryValidation(entryData);
        
        // 设置验证结果变量
        setValidationVariables(validationResult);
        
        return validationResult.isValid;
        
    } catch (error) {
        this.setVariable("ValidationResult", "验证过程发生错误: " + error.message);
        this.setVariable("ValidationError", error.message);
        return false;
    }
}

/**
 * 执行分录验证逻辑
 * @param {Array} entryData - 分录数据数组
 * @returns {Object} 验证结果对象
 */
function performEntryValidation(entryData) {
    var result = {
        isValid: true,
        errors: [],
        warnings: [],
        entryCount: entryData.length,
        totalDebit: 0,
        totalCredit: 0
    };
    
    // 遍历验证每个分录
    for (var i = 0; i < entryData.length; i++) {
        var entry = entryData[i];
        var entryIndex = i + 1;
        
        // 验证必填字段
        validateRequiredFields(entry, entryIndex, result);
        
        // 验证金额字段
        validateAmountFields(entry, entryIndex, result);
        
        // 验证科目代码
        validateAccountCode(entry, entryIndex, result);
        
        // 累计借贷金额
        result.totalDebit += parseFloat(entry.F_DebitAmount || 0);
        result.totalCredit += parseFloat(entry.F_CreditAmount || 0);
    }
    
    // 验证借贷平衡
    validateBalance(result);
    
    // 验证分录数量
    validateEntryCount(result);
    
    return result;
}

/**
 * 验证必填字段
 * @param {Object} entry - 分录对象
 * @param {Number} entryIndex - 分录序号
 * @param {Object} result - 验证结果对象
 */
function validateRequiredFields(entry, entryIndex, result) {
    // 科目代码必填
    if (!entry.F_AccountID || !entry.F_AccountID.FNumber) {
        result.isValid = false;
        result.errors.push("第" + entryIndex + "行：科目代码不能为空");
    }
    
    // 摘要建议填写
    if (!entry.F_Explanation) {
        result.warnings.push("第" + entryIndex + "行：建议填写摘要");
    }
}

/**
 * 验证金额字段
 * @param {Object} entry - 分录对象
 * @param {Number} entryIndex - 分录序号
 * @param {Object} result - 验证结果对象
 */
function validateAmountFields(entry, entryIndex, result) {
    var debitAmount = parseFloat(entry.F_DebitAmount || 0);
    var creditAmount = parseFloat(entry.F_CreditAmount || 0);
    
    // 借贷方不能同时有值
    if (debitAmount > 0 && creditAmount > 0) {
        result.isValid = false;
        result.errors.push("第" + entryIndex + "行：借方和贷方不能同时有金额");
    }
    
    // 借贷方不能同时为空
    if (debitAmount === 0 && creditAmount === 0) {
        result.isValid = false;
        result.errors.push("第" + entryIndex + "行：借方和贷方不能同时为空");
    }
    
    // 金额不能为负数
    if (debitAmount < 0 || creditAmount < 0) {
        result.isValid = false;
        result.errors.push("第" + entryIndex + "行：金额不能为负数");
    }
}

/**
 * 验证科目代码
 * @param {Object} entry - 分录对象
 * @param {Number} entryIndex - 分录序号
 * @param {Object} result - 验证结果对象
 */
function validateAccountCode(entry, entryIndex, result) {
    if (!entry.F_AccountID || !entry.F_AccountID.FNumber) {
        return;
    }
    
    var accountCode = entry.F_AccountID.FNumber;
    
    // 检查现金科目使用限制（示例：1001开头的现金科目）
    if (accountCode.indexOf("1001") === 0) {
        var amount = Math.max(
            parseFloat(entry.F_DebitAmount || 0),
            parseFloat(entry.F_CreditAmount || 0)
        );
        
        // 现金科目金额限制
        if (amount > 10000) {
            result.warnings.push("第" + entryIndex + "行：现金科目金额较大，请确认");
        }
    }
    
    // 检查禁用科目（根据实际业务调整）
    var forbiddenAccounts = ["9999"]; // 示例禁用科目
    for (var j = 0; j < forbiddenAccounts.length; j++) {
        if (accountCode.indexOf(forbiddenAccounts[j]) === 0) {
            result.isValid = false;
            result.errors.push("第" + entryIndex + "行：不允许使用科目 " + accountCode);
            break;
        }
    }
}

/**
 * 验证借贷平衡
 * @param {Object} result - 验证结果对象
 */
function validateBalance(result) {
    var difference = Math.abs(result.totalDebit - result.totalCredit);
    
    // 允许的差额容忍度
    var tolerance = 0.01;
    
    if (difference > tolerance) {
        result.isValid = false;
        result.errors.push("借贷不平衡，差额：" + difference.toFixed(2));
    }
}

/**
 * 验证分录数量
 * @param {Object} result - 验证结果对象
 */
function validateEntryCount(result) {
    // 最少2条分录
    if (result.entryCount < 2) {
        result.isValid = false;
        result.errors.push("分录数量不能少于2条");
    }
    
    // 最多100条分录
    if (result.entryCount > 100) {
        result.isValid = false;
        result.errors.push("分录数量不能超过100条");
    }
}

/**
 * 设置验证结果到流程变量
 * @param {Object} validationResult - 验证结果对象
 */
function setValidationVariables(validationResult) {
    // 设置基本验证结果
    this.setVariable("IsFormValid", validationResult.isValid);
    this.setVariable("EntryCount", validationResult.entryCount);
    this.setVariable("TotalDebitAmount", validationResult.totalDebit);
    this.setVariable("TotalCreditAmount", validationResult.totalCredit);
    
    // 设置错误和警告信息
    this.setVariable("ErrorCount", validationResult.errors.length);
    this.setVariable("WarningCount", validationResult.warnings.length);
    
    if (validationResult.errors.length > 0) {
        this.setVariable("ValidationErrors", validationResult.errors.join("; "));
    }
    
    if (validationResult.warnings.length > 0) {
        this.setVariable("ValidationWarnings", validationResult.warnings.join("; "));
    }
    
    // 设置验证结果摘要
    var resultSummary = validationResult.isValid ? "验证通过" : "验证失败";
    if (validationResult.errors.length > 0) {
        resultSummary += "，错误" + validationResult.errors.length + "个";
    }
    if (validationResult.warnings.length > 0) {
        resultSummary += "，警告" + validationResult.warnings.length + "个";
    }
    
    this.setVariable("ValidationResult", resultSummary);
}

/**
 * 根据业务类型获取验证规则
 * @param {String} businessType - 业务类型
 * @returns {Object} 验证规则对象
 */
function getValidationRulesByBusinessType(businessType) {
    var rules = {
        maxAmount: *********,
        minEntryCount: 2,
        maxEntryCount: 100,
        allowedAccounts: [],
        forbiddenAccounts: []
    };
    
    // 根据业务类型调整规则
    switch (businessType) {
        case "expense":
            rules.maxAmount = 50000;
            rules.allowedAccounts = ["6601", "6602", "6603"];
            break;
        case "payment":
            rules.maxAmount = 1000000;
            rules.forbiddenAccounts = ["1001"];
            break;
        case "receipt":
            rules.allowedAccounts = ["1001", "1002", "1012"];
            break;
    }
    
    return rules;
}

/**
 * 工作流节点主入口函数
 * 在金蝶云苍穹工作流节点中调用此函数
 */
function main() {
    try {
        // 记录开始时间
        this.setVariable("ProcessStartTime", new Date().toISOString());
        
        // 执行表单验证
        var isValid = validateFormEntries(this);
        
        // 根据验证结果设置审批路径
        if (isValid) {
            this.setVariable("ApprovalPath", "normal");
            this.setVariable("ProcessResult", "success");
        } else {
            this.setVariable("ApprovalPath", "reject");
            this.setVariable("ProcessResult", "validation_failed");
        }
        
        // 记录结束时间
        this.setVariable("ProcessEndTime", new Date().toISOString());
        
        return isValid;
        
    } catch (error) {
        this.setVariable("ProcessResult", "error");
        this.setVariable("ProcessError", error.message);
        this.setVariable("ProcessEndTime", new Date().toISOString());
        return false;
    }
}
