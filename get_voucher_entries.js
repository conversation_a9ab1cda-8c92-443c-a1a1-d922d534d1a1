/**
 * KingScript 获取分录脚本
 * 用于从金蝶系统中获取凭证分录信息
 */

// 获取分录的主要函数
function getVoucherEntries(voucherNumber, period) {
    try {
        // 构建查询条件
        var queryCondition = {
            voucherNumber: voucherNumber,
            period: period
        };
        
        // 调用金蝶API获取分录数据
        var entries = queryVoucherEntries(queryCondition);
        
        if (entries && entries.length > 0) {
            console.log("成功获取到 " + entries.length + " 条分录");
            return processEntries(entries);
        } else {
            console.log("未找到符合条件的分录");
            return [];
        }
    } catch (error) {
        console.error("获取分录时发生错误: " + error.message);
        return null;
    }
}

// 查询分录的核心函数
function queryVoucherEntries(condition) {
    // 这里需要根据具体的金蝶API进行调用
    // 示例代码，实际需要根据金蝶系统的API文档进行调整
    
    var sql = "SELECT * FROM t_voucher_entry WHERE 1=1";
    var params = [];
    
    if (condition.voucherNumber) {
        sql += " AND voucher_number = ?";
        params.push(condition.voucherNumber);
    }
    
    if (condition.period) {
        sql += " AND period = ?";
        params.push(condition.period);
    }
    
    // 执行查询
    return executeQuery(sql, params);
}

// 处理分录数据
function processEntries(entries) {
    var processedEntries = [];
    
    for (var i = 0; i < entries.length; i++) {
        var entry = entries[i];
        var processedEntry = {
            entryId: entry.entry_id,
            accountCode: entry.account_code,
            accountName: entry.account_name,
            debitAmount: entry.debit_amount || 0,
            creditAmount: entry.credit_amount || 0,
            summary: entry.summary,
            auxItems: getAuxItems(entry.entry_id)
        };
        
        processedEntries.push(processedEntry);
    }
    
    return processedEntries;
}

// 获取辅助项信息
function getAuxItems(entryId) {
    try {
        var auxSql = "SELECT * FROM t_voucher_entry_aux WHERE entry_id = ?";
        return executeQuery(auxSql, [entryId]);
    } catch (error) {
        console.error("获取辅助项时发生错误: " + error.message);
        return [];
    }
}

// 执行SQL查询的封装函数
function executeQuery(sql, params) {
    // 这里需要根据金蝶系统的数据库连接方式进行实现
    // 示例代码，实际需要根据具体环境调整
    
    try {
        // 假设使用金蝶提供的数据库连接对象
        var connection = getDBConnection();
        var result = connection.executeQuery(sql, params);
        return result;
    } catch (error) {
        throw new Error("数据库查询失败: " + error.message);
    }
}

// 获取数据库连接
function getDBConnection() {
    // 这里需要根据金蝶系统的连接方式进行实现
    // 返回数据库连接对象
    return null; // 占位符
}

// 主入口函数
function main() {
    // 示例调用
    var voucherNumber = "PZ001";
    var period = "202312";
    
    var entries = getVoucherEntries(voucherNumber, period);
    
    if (entries) {
        console.log("获取到的分录信息:");
        for (var i = 0; i < entries.length; i++) {
            var entry = entries[i];
            console.log("科目: " + entry.accountName + 
                       ", 借方: " + entry.debitAmount + 
                       ", 贷方: " + entry.creditAmount);
        }
    }
}

// 导出函数供外部调用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        getVoucherEntries: getVoucherEntries,
        queryVoucherEntries: queryVoucherEntries,
        processEntries: processEntries
    };
}
