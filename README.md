# 金蝶云苍穹工作流KingScript脚本集合

本项目提供了用于金蝶云苍穹流程服务云的KingScript脚本，包括表单分录验证、流程变量管理等工作流功能。

## 📁 文件说明

### 🔧 核心脚本
- `workflow_form_entry_validator.js` - **表单分录验证脚本**
- `workflow_variable_manager.js` - **流程变量管理脚本**  
- `workflow_integration_example.js` - **工作流集成示例脚本**

### 📊 基础功能脚本
- `get_voucher_entries.js` - 分录获取脚本
- `voucher_config.js` - 配置文件
- `workflow_examples_config.js` - 示例配置文件

### 📖 文档
- `README.md` - 使用说明文档

## 🚀 主要功能

### 1. 表单分录验证 (`workflow_form_entry_validator.js`)
- ✅ **必填字段验证** - 科目代码、摘要等必填项检查
- ✅ **金额验证** - 借贷方金额逻辑验证、负数检查
- ✅ **借贷平衡验证** - 自动检查借贷是否平衡
- ✅ **科目代码验证** - 禁用科目、现金科目限额检查
- ✅ **业务规则验证** - 根据业务类型应用不同验证规则
- ✅ **分录数量验证** - 最少/最多分录数量控制

### 2. 流程变量管理 (`workflow_variable_manager.js`)
- ⚙️ **变量设置/获取** - 支持单个和批量操作
- ⚙️ **类型转换** - 自动转换string、number、boolean、date等类型
- ⚙️ **条件化设置** - 根据条件表达式动态设置变量
- ⚙️ **审批级别管理** - 根据金额自动确定审批级别
- ⚙️ **业务变量管理** - 根据业务类型设置特定变量

### 3. 工作流集成 (`workflow_integration_example.js`)
- 🔄 **完整流程处理** - 从验证到审批的完整工作流
- 🔄 **智能审批路径** - 根据验证结果自动选择审批路径
- 🔄 **业务类型识别** - 自动识别费用、付款、收款等业务类型
- 🔄 **错误处理机制** - 完善的异常处理和错误记录

## 📋 使用方法

### 在金蝶云苍穹工作流节点中使用

#### 1. 基本表单验证
```javascript
// 在工作流节点脚本中调用
function main() {
    // 执行表单分录验证
    var isValid = validateFormEntries(this);
    
    if (isValid) {
        // 验证通过，继续正常流程
        this.setVariable("ApprovalPath", "normal");
        return true;
    } else {
        // 验证失败，退回修改
        this.setVariable("ApprovalPath", "reject");
        return false;
    }
}
```

#### 2. 设置流程变量
```javascript
// 设置单个变量
setProcessVariable("TotalAmount", 50000, "number");
setProcessVariable("IsApproved", true, "boolean");

// 批量设置变量
var variables = {
    "ApprovalLevel": "L2",
    "RequiresAttention": true,
    "ProcessedDate": new Date().toISOString()
};
setMultipleVariables(variables);
```

#### 3. 完整工作流处理
```javascript
function main() {
    // 调用集成示例中的主处理函数
    return processWorkflowNode();
}
```

## 🎯 验证规则配置

### 按业务类型的验证规则

#### 费用报销单
- 最大金额：50,000元
- 允许科目：66xx（费用类科目）
- 特殊检查：现金科目限额控制

#### 付款单  
- 最大金额：1,000,000元
- 禁用科目：1001（现金科目，大额付款）
- 特殊检查：合同要求检查

#### 收款单
- 最大金额：5,000,000元  
- 允许科目：10xx（货币资金科目）
- 特殊检查：信用检查要求

## 📊 流程变量说明

### 验证结果变量
- `IsFormValid` - 表单验证是否通过
- `ErrorCount` - 错误数量
- `WarningCount` - 警告数量
- `ValidationErrors` - 错误信息详情
- `ValidationWarnings` - 警告信息详情

### 分录统计变量
- `EntryCount` - 分录数量
- `TotalDebitAmount` - 借方总金额
- `TotalCreditAmount` - 贷方总金额

### 审批流程变量
- `ApprovalPath` - 审批路径（normal/reject）
- `ApprovalLevel` - 审批级别（L1/L2/L3/L4）
- `RequiresAttention` - 是否需要特别关注
- `RejectReason` - 退回原因

### 业务相关变量
- `BusinessType` - 业务类型
- `RequiresCashApproval` - 是否需要现金审批
- `RequiresContract` - 是否需要合同
- `RequiresCreditCheck` - 是否需要信用检查

## 🔧 审批级别配置

| 级别 | 金额范围 | 审批人 | 说明 |
|------|----------|--------|------|
| L1 | ≤ 10,000 | 部门经理 | 一级审批 |
| L2 | 10,001 - 100,000 | 财务经理 | 二级审批 |
| L3 | 100,001 - 1,000,000 | 总经理 | 三级审批 |
| L4 | > 1,000,000 | 董事长 | 四级审批 |

## ⚠️ 注意事项

1. **字段名称适配** - 根据实际表单调整字段名称（如F_DebitAmount、F_CreditAmount等）
2. **分录实体名称** - 根据实际表单调整分录实体名称（FEntity、FBillEntry等）
3. **业务规则定制** - 根据企业实际业务规则调整验证逻辑
4. **科目代码规则** - 根据企业会计科目体系调整科目验证规则
5. **金额限制** - 根据企业管理制度调整各类业务的金额限制

## 🔍 常见问题

### Q: 如何自定义验证规则？
A: 修改`validateBusinessSpecificRules`函数，添加特定的业务验证逻辑。

### Q: 如何调整审批级别？
A: 修改`determineApprovalLevel`函数中的金额阈值。

### Q: 如何添加新的业务类型？
A: 在`determineBusinessType`函数中添加新的业务类型判断逻辑。

### Q: 如何处理自定义字段？
A: 在验证函数中添加对自定义字段的验证逻辑。

## 📞 技术支持

如需技术支持或有问题反馈，请参考：
- [金蝶云社区](https://vip.kingdee.com/)
- [金蝶云苍穹开发文档](https://open.kingdee.com/)
- [工作流开发指南](https://vip.kingdee.com/knowledge)

## 📝 版本历史

- **v1.0.0** - 初始版本，支持基本的表单验证和变量管理功能
- **v1.1.0** - 增加业务类型识别和特定验证规则
- **v1.2.0** - 完善审批级别管理和错误处理机制
