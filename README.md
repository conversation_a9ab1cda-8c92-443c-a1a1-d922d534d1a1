# KingScript 获取分录脚本

本项目提供了用于从金蝶系统中获取凭证分录信息的 KingScript 脚本。

## 文件说明

- `get_voucher_entries.js` - 主要的分录获取脚本
- `voucher_config.js` - 配置文件，包含数据库连接和字段映射
- `README.md` - 使用说明文档

## 功能特性

1. **分录查询** - 支持多种查询条件
   - 按凭证号查询
   - 按期间查询
   - 按科目代码查询

2. **数据处理** - 自动处理和格式化分录数据
   - 借贷金额处理
   - 辅助项信息获取
   - 数据验证和错误处理

3. **灵活配置** - 支持自定义配置
   - 数据库连接参数
   - 字段映射关系
   - 查询模板

## 使用方法

### 1. 基本用法

```javascript
// 引入脚本
var voucherScript = require('./get_voucher_entries.js');

// 获取指定凭证的分录
var entries = voucherScript.getVoucherEntries("PZ001", "202312");

// 处理结果
if (entries && entries.length > 0) {
    entries.forEach(function(entry) {
        console.log("科目: " + entry.accountName);
        console.log("借方: " + entry.debitAmount);
        console.log("贷方: " + entry.creditAmount);
        console.log("摘要: " + entry.summary);
        console.log("---");
    });
}
```

### 2. 配置数据库连接

在 `voucher_config.js` 中修改数据库连接参数：

```javascript
var dbConfig = {
    server: "your_server",
    database: "your_database",
    username: "your_username",
    password: "your_password",
    port: 1433
};
```

### 3. 自定义查询条件

```javascript
// 自定义查询条件
var condition = {
    voucherNumber: "PZ001",
    period: "202312",
    accountCode: "1001%"  // 支持模糊查询
};

var entries = queryVoucherEntries(condition);
```

## API 参考

### getVoucherEntries(voucherNumber, period)

获取指定凭证的分录信息。

**参数:**
- `voucherNumber` (string) - 凭证号
- `period` (string) - 期间，格式：YYYYMM

**返回值:**
- Array - 分录信息数组，每个元素包含：
  - `entryId` - 分录ID
  - `accountCode` - 科目代码
  - `accountName` - 科目名称
  - `debitAmount` - 借方金额
  - `creditAmount` - 贷方金额
  - `summary` - 摘要
  - `auxItems` - 辅助项信息

### queryVoucherEntries(condition)

根据条件查询分录。

**参数:**
- `condition` (object) - 查询条件对象

**返回值:**
- Array - 原始分录数据数组

### processEntries(entries)

处理和格式化分录数据。

**参数:**
- `entries` (array) - 原始分录数据

**返回值:**
- Array - 处理后的分录数据

## 注意事项

1. **数据库权限** - 确保脚本有足够的数据库访问权限
2. **字段映射** - 根据实际的数据库表结构调整字段映射
3. **错误处理** - 脚本包含基本的错误处理，建议根据实际需要增强
4. **性能优化** - 对于大量数据查询，建议添加分页和索引优化

## 常见问题

### Q: 如何处理不同版本的金蝶系统？
A: 修改 `voucher_config.js` 中的字段映射，适配不同版本的表结构。

### Q: 如何添加新的查询条件？
A: 在 `queryVoucherEntries` 函数中添加相应的 SQL 条件判断。

### Q: 如何获取更多的分录字段？
A: 修改查询模板，添加需要的字段，并在 `processEntries` 函数中处理。

## 技术支持

如需技术支持或有问题反馈，请参考金蝶官方文档或联系技术支持团队。

## 版本历史

- v1.0.0 - 初始版本，支持基本的分录查询功能
