/*
 * 金蝶云苍穹工作流集成示例脚本
 * 展示如何在工作流节点中使用表单验证和变量管理功能
 * 基于金蝶云社区最佳实践编写
 */

/**
 * 工作流节点主处理函数
 * 在金蝶云苍穹工作流节点中调用此函数
 * @returns {Boolean} 处理是否成功
 */
function processWorkflowNode() {
    try {
        // 1. 初始化处理
        initializeProcessing();
        
        // 2. 获取表单数据
        var formData = this.getFormData();
        if (!formData) {
            setProcessVariable("ProcessResult", "failed");
            setProcessVariable("FailureReason", "无法获取表单数据");
            return false;
        }
        
        // 3. 获取业务类型
        var businessType = determineBusinessType(formData);
        setProcessVariable("BusinessType", businessType);
        
        // 4. 执行表单验证
        var validationResult = executeFormValidation(formData, businessType);
        
        // 5. 设置验证相关变量
        setVariablesFromValidation(validationResult);
        
        // 6. 根据验证结果处理审批流程
        var processResult = handleApprovalProcess(validationResult, formData);
        
        // 7. 设置最终处理结果
        finalizeProcessing(processResult);
        
        return processResult.success;
        
    } catch (error) {
        handleProcessingError(error);
        return false;
    }
}

/**
 * 初始化处理
 */
function initializeProcessing() {
    setProcessVariable("ProcessStartTime", new Date().toISOString());
    setProcessVariable("ProcessStatus", "processing");
    setProcessVariable("ProcessStep", "initialize");
}

/**
 * 确定业务类型
 * @param {Object} formData - 表单数据
 * @returns {String} 业务类型
 */
function determineBusinessType(formData) {
    // 根据表单标识或字段判断业务类型
    var formId = this.getFormId();
    
    if (formId.indexOf("Expense") >= 0 || formId.indexOf("费用") >= 0) {
        return "expense";
    } else if (formId.indexOf("Payment") >= 0 || formId.indexOf("付款") >= 0) {
        return "payment";
    } else if (formId.indexOf("Receipt") >= 0 || formId.indexOf("收款") >= 0) {
        return "receipt";
    } else if (formId.indexOf("Purchase") >= 0 || formId.indexOf("采购") >= 0) {
        return "purchase";
    } else if (formId.indexOf("Sales") >= 0 || formId.indexOf("销售") >= 0) {
        return "sales";
    }
    
    return "general";
}

/**
 * 执行表单验证
 * @param {Object} formData - 表单数据
 * @param {String} businessType - 业务类型
 * @returns {Object} 验证结果
 */
function executeFormValidation(formData, businessType) {
    setProcessVariable("ProcessStep", "validation");
    
    // 获取分录数据
    var entryData = formData["FEntity"] || formData["FBillEntry"] || [];
    
    if (!entryData || entryData.length === 0) {
        return {
            isValid: false,
            errors: ["分录数据为空"],
            warnings: [],
            entryCount: 0,
            totalDebit: 0,
            totalCredit: 0
        };
    }
    
    // 执行验证逻辑
    var result = {
        isValid: true,
        errors: [],
        warnings: [],
        entryCount: entryData.length,
        totalDebit: 0,
        totalCredit: 0
    };
    
    // 验证每个分录
    for (var i = 0; i < entryData.length; i++) {
        var entry = entryData[i];
        validateSingleEntry(entry, i + 1, result, businessType);
        
        // 累计金额
        result.totalDebit += parseFloat(entry.F_DebitAmount || 0);
        result.totalCredit += parseFloat(entry.F_CreditAmount || 0);
    }
    
    // 验证借贷平衡
    validateBalance(result);
    
    // 验证业务规则
    validateBusinessRules(result, businessType);
    
    return result;
}

/**
 * 验证单个分录
 * @param {Object} entry - 分录对象
 * @param {Number} entryIndex - 分录序号
 * @param {Object} result - 验证结果对象
 * @param {String} businessType - 业务类型
 */
function validateSingleEntry(entry, entryIndex, result, businessType) {
    // 验证必填字段
    if (!entry.F_AccountID || !entry.F_AccountID.FNumber) {
        result.isValid = false;
        result.errors.push("第" + entryIndex + "行：科目代码不能为空");
    }
    
    // 验证金额
    var debitAmount = parseFloat(entry.F_DebitAmount || 0);
    var creditAmount = parseFloat(entry.F_CreditAmount || 0);
    
    if (debitAmount > 0 && creditAmount > 0) {
        result.isValid = false;
        result.errors.push("第" + entryIndex + "行：借方和贷方不能同时有金额");
    }
    
    if (debitAmount === 0 && creditAmount === 0) {
        result.isValid = false;
        result.errors.push("第" + entryIndex + "行：借方和贷方不能同时为空");
    }
    
    // 根据业务类型验证特定规则
    validateBusinessSpecificRules(entry, entryIndex, result, businessType);
}

/**
 * 验证业务特定规则
 * @param {Object} entry - 分录对象
 * @param {Number} entryIndex - 分录序号
 * @param {Object} result - 验证结果对象
 * @param {String} businessType - 业务类型
 */
function validateBusinessSpecificRules(entry, entryIndex, result, businessType) {
    var accountCode = entry.F_AccountID ? entry.F_AccountID.FNumber : "";
    var amount = Math.max(
        parseFloat(entry.F_DebitAmount || 0),
        parseFloat(entry.F_CreditAmount || 0)
    );
    
    switch (businessType) {
        case "expense":
            // 费用报销验证
            if (amount > 50000) {
                result.warnings.push("第" + entryIndex + "行：费用金额较大，需要特殊审批");
            }
            if (accountCode && !accountCode.startsWith("66")) {
                result.warnings.push("第" + entryIndex + "行：建议使用费用类科目");
            }
            break;
            
        case "payment":
            // 付款单验证
            if (accountCode === "1001" && amount > 10000) {
                result.warnings.push("第" + entryIndex + "行：大额现金支付需要特殊审批");
            }
            break;
            
        case "receipt":
            // 收款单验证
            if (accountCode && !accountCode.startsWith("10")) {
                result.warnings.push("第" + entryIndex + "行：建议使用货币资金科目");
            }
            break;
    }
}

/**
 * 验证借贷平衡
 * @param {Object} result - 验证结果对象
 */
function validateBalance(result) {
    var difference = Math.abs(result.totalDebit - result.totalCredit);
    if (difference > 0.01) {
        result.isValid = false;
        result.errors.push("借贷不平衡，差额：" + difference.toFixed(2));
    }
}

/**
 * 验证业务规则
 * @param {Object} result - 验证结果对象
 * @param {String} businessType - 业务类型
 */
function validateBusinessRules(result, businessType) {
    // 验证分录数量
    if (result.entryCount < 2) {
        result.isValid = false;
        result.errors.push("分录数量不能少于2条");
    }
    
    // 验证总金额
    var totalAmount = Math.max(result.totalDebit, result.totalCredit);
    if (totalAmount <= 0) {
        result.isValid = false;
        result.errors.push("总金额必须大于0");
    }
    
    // 根据业务类型验证金额限制
    var maxAmount = getMaxAmountByBusinessType(businessType);
    if (totalAmount > maxAmount) {
        result.warnings.push("总金额超过建议限额，需要高级审批");
    }
}

/**
 * 根据业务类型获取最大金额限制
 * @param {String} businessType - 业务类型
 * @returns {Number} 最大金额
 */
function getMaxAmountByBusinessType(businessType) {
    switch (businessType) {
        case "expense":
            return 50000;
        case "payment":
            return 1000000;
        case "receipt":
            return 5000000;
        default:
            return 999999999;
    }
}

/**
 * 处理审批流程
 * @param {Object} validationResult - 验证结果
 * @param {Object} formData - 表单数据
 * @returns {Object} 处理结果
 */
function handleApprovalProcess(validationResult, formData) {
    setProcessVariable("ProcessStep", "approval");
    
    var processResult = {
        success: true,
        approvalPath: "normal",
        approvalLevel: "L1"
    };
    
    if (!validationResult.isValid) {
        // 验证失败，退回修改
        processResult.success = false;
        processResult.approvalPath = "reject";
        setProcessVariable("ApprovalPath", "reject");
        setProcessVariable("RejectReason", validationResult.errors.join("; "));
        return processResult;
    }
    
    // 根据金额确定审批级别
    var totalAmount = Math.max(validationResult.totalDebit, validationResult.totalCredit);
    processResult.approvalLevel = determineApprovalLevel(totalAmount);
    
    // 设置审批相关变量
    setProcessVariable("ApprovalPath", processResult.approvalPath);
    setProcessVariable("ApprovalLevel", processResult.approvalLevel);
    setProcessVariable("TotalAmount", totalAmount);
    
    // 检查是否需要特殊审批
    if (validationResult.warnings.length > 0) {
        setProcessVariable("RequiresAttention", true);
        setProcessVariable("AttentionReason", validationResult.warnings.join("; "));
    }
    
    return processResult;
}

/**
 * 确定审批级别
 * @param {Number} amount - 金额
 * @returns {String} 审批级别
 */
function determineApprovalLevel(amount) {
    if (amount > 1000000) {
        return "L4"; // 董事长审批
    } else if (amount > 100000) {
        return "L3"; // 总经理审批
    } else if (amount > 10000) {
        return "L2"; // 财务经理审批
    } else {
        return "L1"; // 部门经理审批
    }
}

/**
 * 完成处理
 * @param {Object} processResult - 处理结果
 */
function finalizeProcessing(processResult) {
    setProcessVariable("ProcessStep", "finalize");
    setProcessVariable("ProcessEndTime", new Date().toISOString());
    setProcessVariable("ProcessResult", processResult.success ? "success" : "failed");
    setProcessVariable("ProcessStatus", "completed");
}

/**
 * 处理错误
 * @param {Error} error - 错误对象
 */
function handleProcessingError(error) {
    setProcessVariable("ProcessResult", "error");
    setProcessVariable("ProcessError", error.message);
    setProcessVariable("ProcessEndTime", new Date().toISOString());
    setProcessVariable("ProcessStatus", "error");
}

/**
 * 工作流节点入口函数
 * 这是在金蝶云苍穹工作流节点中实际调用的函数
 */
function main() {
    return processWorkflowNode();
}
